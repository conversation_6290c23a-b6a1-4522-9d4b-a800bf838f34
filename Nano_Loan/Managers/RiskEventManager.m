#import "RiskEventManager.h"
#import "NetworkManager.h"
#import "LocationManager.h"
#import <AdSupport/ASIdentifierManager.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import "DeviceIDManager.h"
#import "AppDelegate.h"

@interface RiskEventManager ()
@end

@implementation RiskEventManager

+ (instancetype)sharedManager {
    static RiskEventManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[RiskEventManager alloc] init];
    });
    return instance;
}

+ (void)reportEventType:(RiskEventType)type
              startTime:(NSTimeInterval)startTime
                endTime:(NSTimeInterval)endTime
                orderId:(nullable NSString *)orderId {
    // 参数准备
    NSString *listener = [NSString stringWithFormat:@"%ld", (long)type];
    NSString *brightly = @"2"; // iOS

    // 设备 IDFV/IDFA
    NSString *idfv = [DeviceIDManager persistentIDFV]; // 使用统一的设备ID管理器
    NSString *idfa = @"";
#if TARGET_OS_SIMULATOR
    // 模拟器无 IDFA
#else
    if (@available(iOS 14, *)) {
        ATTrackingManagerAuthorizationStatus status = [ATTrackingManager trackingAuthorizationStatus];
        if (status == ATTrackingManagerAuthorizationStatusAuthorized) {
            idfa = [ASIdentifierManager sharedManager].advertisingIdentifier.UUIDString ?: @"";
        }
    } else {
        if ([[ASIdentifierManager sharedManager] isAdvertisingTrackingEnabled]) {
            idfa = [ASIdentifierManager sharedManager].advertisingIdentifier.UUIDString ?: @"";
        }
    }
#endif

    // 时间戳（秒级）
    NSString *start = [NSString stringWithFormat:@"%.0f", startTime];
    NSString *end = [NSString stringWithFormat:@"%.0f", endTime];

    // 打印风险事件埋点请求开始标识和当前缓存坐标
    CLLocationCoordinate2D cachedCoord = [[LocationManager sharedManager] getCachedCoordinate];
    NSLog(@"🚨 [RiskEventManager] ===== 风险事件埋点上报开始 =====");
    NSLog(@"🚨 [RiskEventManager] 事件类型: %ld", (long)type);
    NSLog(@"🚨 [RiskEventManager] 请求前缓存坐标: %.6f, %.6f", cachedCoord.latitude, cachedCoord.longitude);
    NSLog(@"🚨 [RiskEventManager] 开始获取实时高精度定位...");

    // 每次风险事件上报都获取实时高精度定位，确保经纬度数据的实时性和精确性
    [[LocationManager sharedManager] getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable locationError) {
        // 构建GPS参数 - 即使定位失败也要上报（使用0坐标）
        NSString *lon = CLLocationCoordinate2DIsValid(coordinate) ? [NSString stringWithFormat:@"%.14f", coordinate.longitude] : @"0";
        NSString *lat = CLLocationCoordinate2DIsValid(coordinate) ? [NSString stringWithFormat:@"%.14f", coordinate.latitude] : @"0";

        if (locationError) {
            NSLog(@"🚨 [RiskEventManager] 定位失败，使用兜底坐标: %@", locationError.localizedDescription);
        } else {
            NSLog(@"🚨 [RiskEventManager] 获取实时定位成功 - 事件类型: %ld, 高精度坐标: %.6f, %.6f", (long)type, coordinate.latitude, coordinate.longitude);
        }

        NSMutableDictionary *params = [@{
            @"listener": listener,
            @"brightly": brightly,
            @"herselfshone": idfv,
            @"bygwendoline": idfa,
            @"hersuch": lon,
            @"invitation": lat,
            @"pawed": start,
            @"letyou": end
        } mutableCopy];

        if (type == RiskEventTypeStartLoan || type == RiskEventTypeEndLoan) {
            if (orderId.length > 0) {
                params[@"gwendolinewas"] = orderId;
            }
        }

        NSLog(@"🚨 [RiskEventManager] 风险事件上报参数: %@", params);

        // 异步上报，不影响用户流程
        [NetworkManager postFormWithAPI:@"Alicia/mayonnaise" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
            if (error) {
                NSLog(@"🚨 [RiskEventManager] 上报失败: %@", error);
            } else {
                NSLog(@"🚨 [RiskEventManager] 上报成功: %@", response);
            }
            NSLog(@"🚨 [RiskEventManager] ===== 风险事件埋点上报结束 =====");
        }];
    }];
}

@end 


/*
上报风控埋点
请求方式 POST 表单
请求地址 "/Alicia/mayonnaise":
场景 上报风控埋点
请求参数
埋点1：开始时间：首次点击发送验证码/ 语音验证码 时间，注册/登录 成功后上报
埋点2：开始时间：点击证件上报按钮时间，结束时间：选择完成证件类型时间
埋点3：开始时间：弹出引导证件正面拍照弹窗时间，结束时间：提交身份证件信息，接口返回成功时间
埋点4：开始时间：点击人脸出现弹窗时间，结束时间：人脸上传成功时间
埋点5-埋点8:开始时间：进入页面时间，结束时间：提交信息成功时间
埋点9：开始时间结束时间一样，点击下一步 调用下单接口成功后上报
埋点10：开始时间结束时间一样，收到 h5 交互函数（确认申请埋点调用方法）后上报
埋点逻辑：每次进入页面页面获取GPS定位，更新当前位置。 提交埋点的时候，没获取到定位则GPS上报0。埋点上报异步进行，不得影响用户正常流程，报错也不要提示用户。埋点GPS上报 不允许做经纬度大于0判断
名称	类型	可选	注释
"listener":	string	否	1=注册 2=证件类型 3=正面 4=自拍 5=个人信息 6=工作信息 7=联系人 8=绑卡 9=开始审贷 10=结束审贷
"brightly":	string	否	1=安卓 2=ios
"herselfshone":	string	否	安卓传and_id ios传idfv
"bygwendoline":	string	否	安卓传gaid ios传idfa
"hersuch":	string	否	经度
"invitation":	string	否	维度
"pawed":	string	否	开始时间 秒级时间戳
"letyou":	string	否	结束时间 秒级时间戳
"gwendolinewas":	string	是	订单号 第10两步必传
返回结果
{
"modest": "0",
"patted": "风控埋点上报成功",
"awkward": {}
}
*/
