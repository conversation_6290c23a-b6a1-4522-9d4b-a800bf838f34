# 埋点问题排查和坐标格式修改说明

## 🔍 埋点2（证件类型选择）排查

### 问题描述
测试人员反馈：埋点2没有上报，2-4应该是3-5埋点

### 排查方法

#### 1. 检查埋点2开始时间设置
```
🔥 [埋点2-证件类型选择] 开始时间记录: 1234567890
```
- 出现此日志说明埋点2开始时间已正确设置

#### 2. 检查跳过条件
```
🔥 [埋点2-证件类型选择] 跳过 - 证件正面已完成
```
- 如果证件正面已完成，不会触发埋点2

#### 3. 检查上报条件
```
🔥 [埋点2-证件类型选择] 跳过上报 - 开始时间未设置 (docSelectStartTime=0)
```
- 如果开始时间为0，不会上报埋点2

#### 4. 正常上报日志
```
🔥 [埋点2-证件类型选择] 开始上报 startTime:1234567890 endTime:1234567900
🚨 [RiskEventManager] ===== 风险事件埋点上报开始 =====
🚨 [RiskEventManager] 事件类型: 2
```

### 埋点2触发流程
1. **开始**: 点击证件上报按钮（前提：证件正面未完成）
2. **结束**: 选择完成证件类型
3. **条件**: `self.info.frontCompleted == NO`

### 测试建议
1. 从全新状态开始测试
2. 确保证件正面未完成
3. 按顺序：点击证件按钮 → 选择证件类型

## 📍 坐标格式修改

### 修改内容
所有坐标打印格式从 `%.14f` 改为 `%.6f`

### 修改后的日志格式
```
🚨 [RiskEventManager] 请求前缓存坐标: 31.123457, 121.987654
🚨 [RiskEventManager] 获取实时定位成功 - 坐标: 31.123460, 121.987651
🏠 [LocationManager] 请求前缓存坐标: 31.123457, 121.987654
🏠 [LocationManager] 位置信息上报 - 开始反向地理编码: 31.123460, 121.987651
```

### 验证实时定位效果
观察 `请求前缓存坐标` 和 `获取实时定位成功` 的坐标差异：
- **有差异**: 实时定位正常工作
- **无差异**: 可能使用了缓存坐标

## 🎯 埋点类型对应关系

| 埋点编号 | 枚举值 | 说明 | 触发时机 |
|---------|--------|------|----------|
| 1 | RiskEventTypeRegister | 注册验证码 | 登录成功后 |
| 2 | RiskEventTypeDocumentSelect | 证件类型选择 | 选择证件类型完成 |
| 3 | RiskEventTypeDocumentFront | 证件正面拍照 | 证件上传成功 |
| 4 | RiskEventTypeFaceRecognition | 人脸识别 | 人脸上传成功 |
| 5 | RiskEventTypePersonalInfo | 个人信息 | 个人信息提交成功 |
| 6 | RiskEventTypeJobInfo | 工作信息 | 工作信息提交成功 |
| 7 | RiskEventTypeContacts | 联系人 | 联系人上传成功 |
| 8 | RiskEventTypeBindCard | 绑卡 | 绑卡成功 |
| 9 | RiskEventTypeStartLoan | 开始审贷 | 点击下一步成功 |
| 10 | RiskEventTypeEndLoan | 结束审贷 | H5确认申请 |

## 🔧 关键修改文件

1. **RiskEventManager.m**: 坐标格式修改为 `.6f`
2. **LocationManager.m**: 坐标格式修改为 `.6f`
3. **IdCardAuthenticationViewController.m**: 增加埋点2调试日志

## 📋 测试检查清单

- [ ] 埋点2开始时间是否正确设置
- [ ] 埋点2是否正常上报
- [ ] 坐标格式是否为6位小数
- [ ] 实时定位是否有坐标差异
- [ ] 各埋点顺序是否正确（1→2→3→4→5...）
