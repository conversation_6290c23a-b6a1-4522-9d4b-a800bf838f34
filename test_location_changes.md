# 高精度定位优化测试说明

## 主要修改内容

### 1. RiskEventManager.m 修改
- **核心变化**: 每次风险事件上报都调用实时定位，不再使用缓存坐标
- **精度提升**: 经纬度格式从 `%f` 改为 `%.14f`，提供更高精度
- **实时性**: 每次埋点都会触发新的定位请求

### 2. LocationManager.m 优化
- **超时时间**: 从20秒增加到30秒，给GPS更多时间获得高精度
- **精度要求**: 最佳精度从1米提升到0.5米
- **距离过滤**: 改为 `kCLDistanceFilterNone`，捕获所有微小移动
- **等待时间**: 最少等待时间从5秒增加到8秒
- **智能判断**: 更严格的精度判断逻辑，需要更多位置更新才接受

### 3. 新增功能
- **强制刷新**: 新增 `forceRefreshLocationCache` 方法
- **详细日志**: 增加大量调试日志，便于追踪定位过程
- **移动检测**: 缓存更新时计算移动距离

## 预期效果

1. **每次埋点都有不同的经纬度**: 因为每次都实时定位
2. **更高精度**: 能检测到客厅到餐桌的微小移动（通常1-3米）
3. **更多电量消耗**: 每次埋点都启动GPS，符合"不惜费电"的要求
4. **更详细的日志**: 便于调试和验证效果

## 测试建议

1. 在不同位置触发风险事件埋点
2. 观察日志中的坐标变化
3. 验证每次埋点的经纬度是否有微小差异
4. 检查定位精度是否达到亚米级（<1米）

## 关键日志标识

- `🚨 [RiskEventManager]`: 风险事件相关日志
- `📍 [LocationManager]`: 位置更新日志  
- `💾 [LocationManager]`: 坐标缓存日志
- `✅`: 成功获得高精度定位
- `⏳`: 正在等待更高精度
